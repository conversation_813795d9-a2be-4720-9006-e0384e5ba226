package com.tfkcolin.practice

import android.Manifest
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.navigation.AppNavGraph
import com.tfkcolin.practice.navigation.Route
import com.tfkcolin.practice.ui.theme.PracticeTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val requestRecordAudioPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        // Handle permission result - this will be passed to ViewModel later
        onRecordAudioPermissionResult(isGranted)
    }

    private var speechViewModel: com.tfkcolin.practice.features.speech.domain.SpeechViewModel? = null
    private lateinit var inAppUpdateManager: InAppUpdateManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Initialize in-app update manager
        inAppUpdateManager = InAppUpdateManager(this)
        inAppUpdateManager.checkForUpdate()

        setContent {
            PracticeTheme {
                val authViewModel: AuthViewModel = hiltViewModel()
                val authState by authViewModel.authState.collectAsStateWithLifecycle()
                val startDest = if (authState.isAuthenticated) Route.Home.path else Route.Auth.path
                Scaffold {
                    AppNavGraph(
                        modifier = Modifier.padding(it),
                        startDestination = startDest,
                        authViewModel = authViewModel,
                        inAppUpdateManager = inAppUpdateManager,
                        onRequestRecordAudioPermission = {
                            requestRecordAudioPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
                        },
                        onSpeechViewModelReady = { viewModel ->
                            speechViewModel = viewModel
                        }
                    )
                }
            }
        }
    }

    @Deprecated("This method has been deprecated in favor of using the Activity Result API\n      which brings increased type safety via an {@link ActivityResultContract} and the prebuilt\n      contracts for common intents available in\n      {@link androidx.activity.result.contract.ActivityResultContracts}, provides hooks for\n      testing, and allow receiving results in separate, testable classes independent from your\n      activity. Use\n      {@link #registerForActivityResult(ActivityResultContract, ActivityResultCallback)}\n      with the appropriate {@link ActivityResultContract} and handling the result in the\n      {@link ActivityResultCallback#onActivityResult(Object) callback}.")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: android.content.Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == InAppUpdateManager.REQUEST_CODE_UPDATE) {
            inAppUpdateManager.handleUpdateResult(resultCode)
        }
    }

    private fun onRecordAudioPermissionResult(granted: Boolean) {
        speechViewModel?.onPermissionResult(granted)
    }
}

