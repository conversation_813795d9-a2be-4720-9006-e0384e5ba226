package com.tfkcolin.practice.di

import android.app.Application
import android.content.Context
import com.google.firebase.auth.FirebaseAuth
import com.tfkcolin.practice.data.engine.AndroidSpeechEngine
import com.tfkcolin.practice.data.engine.AndroidTtsEngine
import com.tfkcolin.practice.data.engine.GoogleCloudTtsEngine
import com.tfkcolin.practice.features.tts.data.TtsRepository
import com.tfkcolin.practice.features.auth.data.AuthRepository
import com.tfkcolin.practice.features.speech.data.SpeechRepository
import com.tfkcolin.practice.features.language.domain.LanguageManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.android.scopes.ViewModelScoped
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object TtsModule {
    @Provides
    @Singleton
    fun provideTtsRepository(
        @ApplicationContext context: Context,
    ): TtsRepository {
        val engines = mapOf(
            "android" to AndroidTtsEngine(context as Application),
            "google" to GoogleCloudTtsEngine()
        )
        return TtsRepository(engines)
    }
}

@Module
@InstallIn(SingletonComponent::class)
object AuthModule {
    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth = FirebaseAuth.getInstance()

    @Provides
    @Singleton
    fun provideAuthRepository(
        firebaseAuth: FirebaseAuth
    ): AuthRepository = AuthRepository(firebaseAuth)
}

@Module
@InstallIn(SingletonComponent::class)
object SpeechModule {
    @Provides
    @Singleton
    fun provideSpeechRepository(
        @ApplicationContext context: Context,
    ): SpeechRepository {
        val engines = mapOf(
            "android" to AndroidSpeechEngine(context as Application)
        )
        return SpeechRepository(engines)
    }
}

@Module
@InstallIn(SingletonComponent::class)
object LanguageModule {
    @Provides
    @Singleton
    fun provideLanguageManager(
        ttsRepository: TtsRepository,
        speechRepository: SpeechRepository
    ): LanguageManager = LanguageManager(ttsRepository, speechRepository)
}
