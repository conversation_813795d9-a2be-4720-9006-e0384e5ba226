package com.tfkcolin.practice.core

import android.app.Activity
import android.content.Context
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class InAppUpdateManager(private val context: Context) {

    private val appUpdateManager: AppUpdateManager = AppUpdateManagerFactory.create(context)

    private val _updateAvailable = MutableStateFlow(false)
    val updateAvailable: StateFlow<Boolean> = _updateAvailable

    fun checkForUpdate() {
        appUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo ->
            val isUpdateAvailable = appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                    && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE)
            _updateAvailable.value = isUpdateAvailable
        }.addOnFailureListener {
            _updateAvailable.value = false
        }
    }

    fun startUpdateFlow(activity: Activity) {
        appUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE)
            ) {
                appUpdateManager.startUpdateFlowForResult(
                    appUpdateInfo,
                    AppUpdateType.FLEXIBLE,
                    activity,
                    REQUEST_CODE_UPDATE
                )
            }
        }
    }

    fun handleUpdateResult(resultCode: Int): Boolean {
        return if (resultCode == Activity.RESULT_OK) {
            // Update was successful or user accepted the update
            _updateAvailable.value = false
            true
        } else if (resultCode == Activity.RESULT_CANCELED) {
            // User cancelled the update
            false
        } else {
            false
        }
    }

    companion object {
        const val REQUEST_CODE_UPDATE = 123
    }
}