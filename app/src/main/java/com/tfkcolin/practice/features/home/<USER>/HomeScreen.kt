package com.tfkcolin.practice.features.home.ui

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.core.ui.UpdateIcon
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.features.home.domain.HomeViewModel
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    authViewModel: AuthViewModel,
    inAppUpdateManager: InAppUpdateManager,
    onNavigateToListeningPractice: () -> Unit = {},
    onNavigateToConversationPractice: () -> Unit = {},
    onRequestRecordAudioPermission: () -> Unit = {},
    modifier: Modifier = Modifier,
    homeViewModel: HomeViewModel = hiltViewModel()
) {
    val activity = LocalActivity.current
    val uiState by homeViewModel.uiState.collectAsStateWithLifecycle()
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(PrimaryWhite)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Top bar with update icon and logout button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if(activity != null) {
                UpdateIcon(
                    updateManager = inAppUpdateManager,
                    onUpdateClick = {
                        inAppUpdateManager.startUpdateFlow(activity)
                    }
                )
            }
            IconButton(onClick = { authViewModel.signOut() }) {
                Icon(
                    Icons.AutoMirrored.Filled.ExitToApp, 
                    contentDescription = "Logout",
                    tint = PrimaryBlack
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Welcome section
        Text(
            text = "Language Practice",
            style = MaterialTheme.typography.headlineLarge,
            fontWeight = FontWeight.Bold,
            color = PrimaryBlack,
            textAlign = TextAlign.Center
        )
        
        Text(
            text = "Choose your practice mode",
            style = MaterialTheme.typography.bodyLarge,
            color = DarkGray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp)
        )
        
        Spacer(modifier = Modifier.height(48.dp))
        
        // Large microphone button for conversation recording
        Card(
            onClick = {
                if (uiState.isRecording) {
                    homeViewModel.stopRecording()
                } else {
                    onRequestRecordAudioPermission()
                    homeViewModel.startRecording()
                }
            },
            modifier = Modifier
                .size(200.dp)
                .padding(16.dp),
            shape = CircleShape,
            colors = CardDefaults.cardColors(
                containerColor = if (uiState.isRecording) Color.Red else AccentBlue
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                if (uiState.isRecording) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Stop,
                            contentDescription = "Stop Recording",
                            modifier = Modifier.size(60.dp),
                            tint = PrimaryWhite
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = formatDuration(uiState.recordingDuration),
                            color = PrimaryWhite,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                } else {
                    Icon(
                        imageVector = Icons.Default.Mic,
                        contentDescription = "Record Conversation",
                        modifier = Modifier.size(80.dp),
                        tint = PrimaryWhite
                    )
                }
            }
        }
        
        Text(
            text = if (uiState.isRecording) "Recording..." else "Record Conversation",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = PrimaryBlack,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 16.dp)
        )

        Text(
            text = if (uiState.isRecording) "Tap to stop recording" else "Tap to record everyday conversations",
            style = MaterialTheme.typography.bodyMedium,
            color = DarkGray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 4.dp)
        )
        
        Spacer(modifier = Modifier.height(48.dp))
        
        // Practice mode cards
        PracticeCard(
            title = "Listening & Speaking",
            description = "Practice pronunciation and listening comprehension",
            icon = Icons.Default.VolumeUp,
            onClick = onNavigateToListeningPractice,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        PracticeCard(
            title = "AI Conversation",
            description = "Have interactive conversations with AI tutor",
            icon = Icons.Default.Chat,
            onClick = onNavigateToConversationPractice,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.weight(1f))

        // Error handling
        uiState.errorMessage?.let { error ->
            LaunchedEffect(error) {
                // Show error message (could be a snackbar in a real app)
                homeViewModel.clearError()
            }
        }
    }
}

private fun formatDuration(durationMs: Long): String {
    val seconds = (durationMs / 1000) % 60
    val minutes = (durationMs / (1000 * 60)) % 60
    return String.format("%02d:%02d", minutes, seconds)
}

@Composable
private fun PracticeCard(
    title: String,
    description: String,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = LightGray
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(56.dp)
                    .clip(CircleShape)
                    .background(AccentBlue),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = PrimaryWhite,
                    modifier = Modifier.size(28.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = PrimaryBlack
                )
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = DarkGray,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = "Navigate",
                tint = DarkGray,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}
