package com.tfkcolin.practice.features.auth.domain

data class AuthState(
    val isLoading: Boolean = false,
    val isAuthenticated: Boolean = false,
    val user: User? = null,
    val error: String? = null,
    val successMessage: String? = null
)

data class User(
    val id: String,
    val email: String,
    val displayName: String? = null,
    val isPremium: Boolean = false,
    val createdAt: Long? = null
)