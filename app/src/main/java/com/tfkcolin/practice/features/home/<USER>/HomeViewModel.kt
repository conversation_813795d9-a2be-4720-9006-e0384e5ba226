package com.tfkcolin.practice.features.home.domain

import android.content.Context
import android.media.MediaRecorder
import android.os.Build
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

data class HomeUiState(
    val isRecording: Boolean = false,
    val recordingDuration: Long = 0L,
    val errorMessage: String? = null,
    val lastRecordingPath: String? = null
)

@HiltViewModel
class HomeViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    private var mediaRecorder: MediaRecorder? = null
    private var recordingStartTime: Long = 0L
    private var recordingFile: File? = null

    fun startRecording() {
        viewModelScope.launch {
            try {
                val recordingsDir = File(context.filesDir, "recordings")
                if (!recordingsDir.exists()) {
                    recordingsDir.mkdirs()
                }

                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                recordingFile = File(recordingsDir, "conversation_$timestamp.m4a")

                mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    MediaRecorder(context)
                } else {
                    @Suppress("DEPRECATION")
                    MediaRecorder()
                }.apply {
                    setAudioSource(MediaRecorder.AudioSource.MIC)
                    setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                    setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                    setOutputFile(recordingFile?.absolutePath)
                    
                    prepare()
                    start()
                }

                recordingStartTime = System.currentTimeMillis()
                _uiState.value = _uiState.value.copy(
                    isRecording = true,
                    errorMessage = null
                )

                // Start duration tracking
                startDurationTracking()

            } catch (e: IOException) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to start recording: ${e.message}"
                )
            } catch (e: SecurityException) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Microphone permission required"
                )
            }
        }
    }

    fun stopRecording() {
        viewModelScope.launch {
            try {
                mediaRecorder?.apply {
                    stop()
                    release()
                }
                mediaRecorder = null

                _uiState.value = _uiState.value.copy(
                    isRecording = false,
                    recordingDuration = 0L,
                    lastRecordingPath = recordingFile?.absolutePath
                )

            } catch (e: RuntimeException) {
                _uiState.value = _uiState.value.copy(
                    isRecording = false,
                    recordingDuration = 0L,
                    errorMessage = "Failed to stop recording: ${e.message}"
                )
            }
        }
    }

    private fun startDurationTracking() {
        viewModelScope.launch {
            while (_uiState.value.isRecording) {
                val duration = System.currentTimeMillis() - recordingStartTime
                _uiState.value = _uiState.value.copy(recordingDuration = duration)
                kotlinx.coroutines.delay(100) // Update every 100ms
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun getRecordingFiles(): List<File> {
        val recordingsDir = File(context.filesDir, "recordings")
        return if (recordingsDir.exists()) {
            recordingsDir.listFiles()?.filter { it.extension == "m4a" }?.sortedByDescending { it.lastModified() } ?: emptyList()
        } else {
            emptyList()
        }
    }

    override fun onCleared() {
        super.onCleared()
        mediaRecorder?.release()
        mediaRecorder = null
    }
}
