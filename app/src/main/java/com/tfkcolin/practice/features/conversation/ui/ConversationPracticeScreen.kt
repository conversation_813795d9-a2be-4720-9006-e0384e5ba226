package com.tfkcolin.practice.features.conversation.ui

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.conversation.domain.ConversationViewModel
import com.tfkcolin.practice.features.conversation.domain.ConversationMessage
import com.tfkcolin.practice.ui.components.*
import com.tfkcolin.practice.ui.theme.*



@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConversationPracticeScreen(
    onClose: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: ConversationViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(BackgroundGray)
    ) {
        // Top bar
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onClose) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close",
                    tint = PrimaryBlack
                )
            }
            
            Text(
                text = "Free Talk",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack
            )
            
            // Placeholder for balance
            Spacer(modifier = Modifier.width(48.dp))
        }

        // Conversation messages
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(vertical = 16.dp)
        ) {
            items(uiState.messages) { message ->
                ConversationMessageItem(
                    message = message,
                    onPlayAudio = { text -> viewModel.playAudio(text) }
                )
            }
        }

        // Input section
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(PrimaryWhite)
                .padding(16.dp)
        ) {
            // Current input display
            if (uiState.currentInput.isNotEmpty()) {
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(12.dp),
                    color = LightGray
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = uiState.currentInput,
                            fontSize = 16.sp,
                            color = PrimaryBlack,
                            modifier = Modifier.weight(1f)
                        )
                        IconButton(onClick = { viewModel.clearInput() }) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear",
                                tint = DarkGray
                            )
                        }
                    }
                }
            }

            // Try saying section
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                color = LightGray,
                border = BorderStroke(1.dp, MediumGray)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "TRY SAYING:",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        color = DarkGray,
                        letterSpacing = 0.5.sp
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = uiState.suggestedResponse,
                            fontSize = 16.sp,
                            color = PrimaryBlack,
                            modifier = Modifier.weight(1f)
                        )

                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            IconButton(onClick = { viewModel.playAudio(uiState.suggestedResponse) }) {
                                Icon(
                                    imageVector = Icons.Default.VolumeUp,
                                    contentDescription = "Play audio",
                                    tint = DarkGray
                                )
                            }
                            IconButton(onClick = { /* Translate */ }) {
                                Icon(
                                    imageVector = Icons.Default.Translate,
                                    contentDescription = "Translate",
                                    tint = DarkGray
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Voice input section
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Cancel button
                Box(
                    modifier = Modifier
                        .size(56.dp)
                        .clip(CircleShape)
                        .background(ErrorRed.copy(alpha = 0.1f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Cancel",
                        tint = ErrorRed,
                        modifier = Modifier.size(24.dp)
                    )
                }

                // Microphone button
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(CircleShape)
                        .background(if (uiState.isListening) ErrorRed else AccentBlue)
                        .clickable {
                            if (uiState.isListening) {
                                viewModel.stopListening()
                            } else {
                                viewModel.startListening()
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = if (uiState.isListening) Icons.Default.Stop else Icons.Default.Mic,
                        contentDescription = if (uiState.isListening) "Stop" else "Speak",
                        tint = PrimaryWhite,
                        modifier = Modifier.size(32.dp)
                    )
                }

                // Placeholder for balance
                Spacer(modifier = Modifier.size(56.dp))
            }

            // Audio visualization (simplified)
            if (uiState.isListening) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    repeat(15) { index ->
                        Box(
                            modifier = Modifier
                                .width(3.dp)
                                .height((10..30).random().dp)
                                .background(
                                    color = AccentBlue.copy(alpha = 0.7f),
                                    shape = RoundedCornerShape(1.5.dp)
                                )
                        )
                        if (index < 14) Spacer(modifier = Modifier.width(2.dp))
                    }
                }
            }
        }
    }
}

@Composable
private fun ConversationMessageItem(
    message: ConversationMessage,
    onPlayAudio: (String) -> Unit
) {
    Column {
        if (!message.isUser) {
            // AI message with controls
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                color = LightGray
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = message.text,
                        fontSize = 16.sp,
                        color = PrimaryBlack,
                        lineHeight = 20.sp
                    )
                    
                    if (message.hasAudio) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            IconButton(onClick = { onPlayAudio(message.text) }) {
                                Icon(
                                    imageVector = Icons.Default.VolumeUp,
                                    contentDescription = "Play audio",
                                    tint = DarkGray,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                            IconButton(onClick = { /* Translate */ }) {
                                Icon(
                                    imageVector = Icons.Default.Translate,
                                    contentDescription = "Translate",
                                    tint = DarkGray,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                            IconButton(onClick = { /* More options */ }) {
                                Icon(
                                    imageVector = Icons.Default.MoreHoriz,
                                    contentDescription = "More options",
                                    tint = DarkGray,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                            IconButton(onClick = { /* Flag */ }) {
                                Icon(
                                    imageVector = Icons.Default.Flag,
                                    contentDescription = "Flag",
                                    tint = DarkGray,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                    }
                }
            }
        } else {
            // User message
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                Surface(
                    shape = RoundedCornerShape(12.dp),
                    color = AccentBlue
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (message.isCorrection) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Correct",
                                tint = PrimaryWhite,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        Text(
                            text = message.text,
                            color = PrimaryWhite,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}


