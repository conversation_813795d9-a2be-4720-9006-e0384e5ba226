package com.tfkcolin.practice.features.speech.ui

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.core.ui.UpdateIcon
import com.tfkcolin.practice.features.speech.domain.SpeechViewModel
import com.tfkcolin.practice.features.auth.domain.AuthViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SpeechMainScreen(
    viewModel: SpeechViewModel,
    authViewModel: AuthViewModel,
    inAppUpdateManager: InAppUpdateManager,
    onOpenSettings: () -> Unit = {},
    onSwitchToTts: () -> Unit = {},
    onOpenConversationPractice: () -> Unit = {},
    onRequestRecordAudioPermission: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState.collectAsStateWithLifecycle()
    val activity = LocalActivity.current
    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Top
    ) {
        // App bar with update icon and logout button
        Row(
            modifier = Modifier.fillMaxWidth().padding(16.dp),
            horizontalArrangement = Arrangement.End
        ) {
            if(activity != null) {
                UpdateIcon(
                    updateManager = inAppUpdateManager,
                    onUpdateClick = {
                        inAppUpdateManager.startUpdateFlow(activity)
                    }
                )
            }
            IconButton(onClick = { authViewModel.signOut() }) {
                Icon(Icons.AutoMirrored.Filled.ExitToApp, contentDescription = "Logout")
            }
        }
        
        // Feature selector tabs
        TabRow(selectedTabIndex = 1) {
            Tab(
                selected = false,
                onClick = onSwitchToTts,
                text = { Text("Text-to-Speech") }
            )
            Tab(
                selected = true,
                onClick = { },
                text = { Text("Speech Recognition") }
            )
        }
        
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Text(
                text = "Conversation Practice",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Practice your conversation skills with AI",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.height(48.dp))

            // Start Conversation Card
            Card(
                onClick = onOpenConversationPractice,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Start Conversation",
                        style = MaterialTheme.typography.titleLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Begin an interactive conversation with AI tutor",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Back to Practice Selection
            OutlinedButton(
                onClick = onSwitchToTts,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Back to Practice Selection")
            }



        }
    }
}

