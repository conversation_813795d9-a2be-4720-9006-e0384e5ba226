package com.tfkcolin.practice.features.tts.ui

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.core.ui.UpdateIcon
import com.tfkcolin.practice.features.tts.domain.TtsUiState
import com.tfkcolin.practice.features.tts.domain.TtsViewModel
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.core.ui.EngineSelector

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TtsMainScreen(
    viewModel: TtsViewModel,
    authViewModel: AuthViewModel,
    inAppUpdateManager: InAppUpdateManager,
    onOpenSettings: () -> Unit = {},
    onSwitchToSpeech: () -> Unit = {},
    onOpenListeningPractice: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState.collectAsStateWithLifecycle()
    val activity = LocalActivity.current
    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Top
    ) {
        // App bar with update icon and logout button
        Row(
            modifier = Modifier.fillMaxWidth().padding(16.dp),
            horizontalArrangement = Arrangement.End
        ) {
            if(activity != null) {
                UpdateIcon(
                    updateManager = inAppUpdateManager,
                    onUpdateClick = {
                        inAppUpdateManager.startUpdateFlow(activity)
                    }
                )
            }
            IconButton(onClick = { authViewModel.signOut() }) {
                Icon(Icons.AutoMirrored.Filled.ExitToApp, contentDescription = "Logout")
            }
        }
        
        // Feature selector tabs
        TabRow(selectedTabIndex = 0) {
            Tab(
                selected = true,
                onClick = { },
                text = { Text("Text-to-Speech") }
            )
            Tab(
                selected = false,
                onClick = onSwitchToSpeech,
                text = { Text("Speech Recognition") }
            )
        }
        
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Text(
                text = "Choose Your Practice",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Select a practice mode to improve your language skills",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.height(48.dp))

            // Listening & Speaking Practice Card
            Card(
                onClick = onOpenListeningPractice,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Listening & Speaking",
                        style = MaterialTheme.typography.titleLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Practice pronunciation and listening comprehension",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Conversation Practice Card
            Card(
                onClick = onSwitchToSpeech,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Conversation Practice",
                        style = MaterialTheme.typography.titleLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Have interactive conversations with AI",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

