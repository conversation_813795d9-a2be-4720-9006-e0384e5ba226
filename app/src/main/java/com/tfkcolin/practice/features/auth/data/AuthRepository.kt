package com.tfkcolin.practice.features.auth.data

import android.content.Context
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.exceptions.GetCredentialException
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.userProfileChangeRequest
import com.tfkcolin.practice.features.auth.domain.AuthState
import com.tfkcolin.practice.features.auth.domain.User
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val firebaseAuth: FirebaseAuth
) {
    companion object {
        private const val GOOGLE_WEB_CLIENT_ID = "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com"
    }
    private val _authState = MutableStateFlow<AuthState>(AuthState())
    val authState: StateFlow<AuthState> = _authState

    init {
        firebaseAuth.addAuthStateListener { auth ->
            val firebaseUser = auth.currentUser
            val user = firebaseUser?.let { mapFirebaseUserToUser(it) }
            _authState.value = AuthState(
                isAuthenticated = firebaseUser != null,
                user = user
            )
        }
    }

    suspend fun signUp(
        email: String,
        password: String,
        displayName: String? = null
    ): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null)
            val result = firebaseAuth.createUserWithEmailAndPassword(email, password).await()
            result.user?.let { firebaseUser ->
                // Update display name if provided
                if (!displayName.isNullOrEmpty()) {
                    firebaseUser.updateProfile(
                        userProfileChangeRequest {
                            this.displayName = displayName
                        }
                    ).await()
                }
            }
            val successMessage = if (displayName.isNullOrEmpty()) "Account created successfully!" else "Account created successfully!"
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = null,
                successMessage = successMessage
            )
            Result.success(Unit)
        } catch (e: Exception) {
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = e.localizedMessage ?: "Sign up failed",
                successMessage = null
            )
            Result.failure(e)
        }
    }

    suspend fun signIn(email: String, password: String): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null)
            firebaseAuth.signInWithEmailAndPassword(email, password).await()
            val currentUser = firebaseAuth.currentUser
            val welcomeMessage = if (currentUser?.displayName != null) {
                "Welcome back, ${currentUser.displayName}!"
            } else {
                "Welcome back!"
            }
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = null,
                successMessage = welcomeMessage
            )
            Result.success(Unit)
        } catch (e: Exception) {
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = e.localizedMessage ?: "Sign in failed",
                successMessage = null
            )
            Result.failure(e)
        }
    }

    suspend fun signOut(): Result<Unit> {
        return try {
            firebaseAuth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun resetPassword(email: String): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null, successMessage = null)
            firebaseAuth.sendPasswordResetEmail(email).await()
            _authState.value = _authState.value.copy(
                isLoading = false,
                successMessage = "Password reset email sent to $email"
            )
            Result.success(Unit)
        } catch (e: Exception) {
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = e.localizedMessage ?: "Password reset failed",
                successMessage = null
            )
            Result.failure(e)
        }
    }

    fun getCurrentUser(): User? {
        return firebaseAuth.currentUser?.let { mapFirebaseUserToUser(it) }
    }

    suspend fun signInWithGoogle(context: Context): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null)

            val credentialManager = CredentialManager.create(context)

            val googleIdOption = GetGoogleIdOption.Builder()
                .setFilterByAuthorizedAccounts(false)
                .setServerClientId(GOOGLE_WEB_CLIENT_ID)
                .build()

            val request = GetCredentialRequest.Builder()
                .addCredentialOption(googleIdOption)
                .build()

            val result = credentialManager.getCredential(
                request = request,
                context = context,
            )

            val credential = result.credential
            val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
            val googleIdToken = googleIdTokenCredential.idToken

            val firebaseCredential = GoogleAuthProvider.getCredential(googleIdToken, null)
            firebaseAuth.signInWithCredential(firebaseCredential).await()

            val currentUser = firebaseAuth.currentUser
            val welcomeMessage = if (currentUser?.displayName != null) {
                "Welcome, ${currentUser.displayName}!"
            } else {
                "Welcome!"
            }

            _authState.value = _authState.value.copy(
                isLoading = false,
                error = null,
                successMessage = welcomeMessage
            )
            Result.success(Unit)
        } catch (e: GetCredentialException) {
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = "Google Sign-In failed: ${e.localizedMessage}",
                successMessage = null
            )
            Result.failure(e)
        } catch (e: Exception) {
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = "Google Sign-In failed: ${e.localizedMessage}",
                successMessage = null
            )
            Result.failure(e)
        }
    }

    fun clearError() {
        _authState.value = _authState.value.copy(error = null, successMessage = null)
    }

    private fun mapFirebaseUserToUser(firebaseUser: FirebaseUser): User {
        return User(
            id = firebaseUser.uid,
            email = firebaseUser.email ?: "",
            displayName = firebaseUser.displayName,
            createdAt = firebaseUser.metadata?.creationTimestamp
        )
    }
}