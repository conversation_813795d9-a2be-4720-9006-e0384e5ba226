package com.tfkcolin.practice.features.language.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.language.domain.LanguageSelectionViewModel
import com.tfkcolin.practice.features.language.domain.SupportedLanguage
import com.tfkcolin.practice.ui.components.LanguageSelectionItem
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageSelectionScreen(
    onLanguageSelected: (SupportedLanguage) -> Unit = {},
    onBackPressed: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: LanguageSelectionViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(PrimaryWhite)
    ) {
        // Top bar with back button
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackPressed) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = PrimaryBlack
                )
            }
        }

        // Title
        Text(
            text = "What language do you want to learn?",
            fontSize = 24.sp,
            fontWeight = FontWeight.SemiBold,
            color = PrimaryBlack,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp, vertical = 16.dp),
            lineHeight = 30.sp
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Loading state
        if (state.isInitializing) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = AccentBlue
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Detecting available languages...",
                        fontSize = 16.sp,
                        color = DarkGray,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }

        // Error state
        else if (state.error != null) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Error loading languages",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = ErrorRed
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = state.error!!,
                    fontSize = 14.sp,
                    color = DarkGray,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = { viewModel.retryInitialization() }
                ) {
                    Text("Retry")
                }
            }
        }

        // Language list
        else {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                state.supportedLanguages.forEach { language ->
                    LanguageSelectionItem(
                        language = language.displayName,
                        isSelected = language == state.selectedLanguage,
                        isComingSoon = false,
                        onClick = {
                            viewModel.selectLanguage(language)
                            onLanguageSelected(language)
                        }
                    )
                }

                if (state.supportedLanguages.isEmpty() && !state.isInitializing) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No supported languages found",
                            fontSize = 16.sp,
                            color = DarkGray,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // Bottom branding
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Speak",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = PrimaryBlack
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "curated by",
                fontSize = 14.sp,
                color = DarkGray
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Mobbin",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = PrimaryBlack
            )
        }
    }
}
