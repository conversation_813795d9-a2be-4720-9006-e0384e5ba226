package com.tfkcolin.practice.features.auth.domain

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.features.auth.data.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    val authState = authRepository.authState

    fun signUp(email: String, password: String, displayName: String?) {
        viewModelScope.launch {
            authRepository.signUp(email, password, displayName)
        }
    }

    fun signIn(email: String, password: String) {
        viewModelScope.launch {
            authRepository.signIn(email, password)
        }
    }

    fun signOut() {
        viewModelScope.launch {
            authRepository.signOut()
        }
    }

    fun resetPassword(email: String) {
        viewModelScope.launch {
            authRepository.resetPassword(email)
        }
    }

    fun signInWithGoogle(context: Context) {
        viewModelScope.launch {
            authRepository.signInWithGoogle(context)
        }
    }

    fun clearError() {
        // Clear error state through repository
        authRepository.clearError()
    }
}