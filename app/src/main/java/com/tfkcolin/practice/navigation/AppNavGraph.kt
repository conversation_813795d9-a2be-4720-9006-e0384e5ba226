package com.tfkcolin.practice.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.features.auth.ui.AuthScreen
import com.tfkcolin.practice.features.speech.domain.SpeechViewModel
import com.tfkcolin.practice.features.language.ui.LanguageSelectionScreen
import com.tfkcolin.practice.features.practice.ui.ListeningSpeakingPracticeScreen
import com.tfkcolin.practice.features.conversation.ui.ConversationPracticeScreen
import com.tfkcolin.practice.features.home.ui.HomeScreen

@Composable
fun AppNavGraph(
    modifier: Modifier = Modifier,
    startDestination: String,
    authViewModel: AuthViewModel,
    inAppUpdateManager: InAppUpdateManager,
    onRequestRecordAudioPermission: () -> Unit = {},
    onSpeechViewModelReady: (SpeechViewModel) -> Unit = {},
    navController: NavHostController = rememberNavController()
) {
    NavHost(modifier = modifier, navController = navController, startDestination = startDestination) {
        // Authentication screen
        composable(Route.Auth.path) {
            val authState = authViewModel.authState.collectAsStateWithLifecycle()
            
            // Handle navigation when user successfully logs in
            LaunchedEffect(authState.value.isAuthenticated) {
                if (authState.value.isAuthenticated) {
                    navController.navigate(Route.LanguageSelection.path) {
                        popUpTo(Route.Auth.path) { inclusive = true }
                    }
                }
            }
            
            AuthScreen(authViewModel = authViewModel)
        }

        // Language Selection screen
        composable(Route.LanguageSelection.path) {
            LanguageSelectionScreen(
                onLanguageSelected = { language ->
                    // Navigate to home screen
                    navController.navigate(Route.Home.path) {
                        popUpTo(Route.LanguageSelection.path) { inclusive = true }
                    }
                },
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }

        // Home screen
        composable(Route.Home.path) {
            val authState = authViewModel.authState.collectAsStateWithLifecycle()

            // Handle logout navigation
            LaunchedEffect(authState.value.isAuthenticated) {
                if (!authState.value.isAuthenticated) {
                    navController.navigate(Route.Auth.path) {
                        popUpTo(0) { inclusive = true } // Clear entire backstack
                    }
                }
            }

            HomeScreen(
                authViewModel = authViewModel,
                inAppUpdateManager = inAppUpdateManager,
                onNavigateToListeningPractice = { navController.navigate(Route.ListeningSpeakingPractice.path) },
                onNavigateToConversationPractice = { navController.navigate(Route.ConversationPractice.path) },
                onRequestRecordAudioPermission = onRequestRecordAudioPermission
            )
        }

        // Listening/Speaking Practice screen
        composable(Route.ListeningSpeakingPractice.path) {
            ListeningSpeakingPracticeScreen(
                onClose = { navController.popBackStack() }
            )
        }

        // Conversation Practice screen
        composable(Route.ConversationPractice.path) {
            ConversationPracticeScreen(
                onClose = { navController.popBackStack() }
            )
        }
    }
}
