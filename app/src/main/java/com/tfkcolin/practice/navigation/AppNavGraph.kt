package com.tfkcolin.practice.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.features.auth.ui.AuthScreen
import com.tfkcolin.practice.features.speech.domain.SpeechViewModel
import com.tfkcolin.practice.features.speech.ui.SpeechMainScreen
import com.tfkcolin.practice.features.tts.domain.TtsViewModel
import com.tfkcolin.practice.features.tts.ui.TtsMainScreen
import com.tfkcolin.practice.features.language.ui.LanguageSelectionScreen
import com.tfkcolin.practice.features.practice.ui.ListeningSpeakingPracticeScreen
import com.tfkcolin.practice.features.conversation.ui.ConversationPracticeScreen

@Composable
fun AppNavGraph(
    startDestination: String,
    authViewModel: AuthViewModel,
    inAppUpdateManager: InAppUpdateManager,
    onRequestRecordAudioPermission: () -> Unit = {},
    onSpeechViewModelReady: (com.tfkcolin.practice.features.speech.domain.SpeechViewModel) -> Unit = {},
    navController: NavHostController = rememberNavController()
) {
    NavHost(navController = navController, startDestination = startDestination) {
        
        // Authentication screen
        composable(Route.Auth.path) {
            val authState = authViewModel.authState.collectAsStateWithLifecycle()
            
            // Handle navigation when user successfully logs in
            LaunchedEffect(authState.value.isAuthenticated) {
                if (authState.value.isAuthenticated) {
                    navController.navigate(Route.LanguageSelection.path) {
                        popUpTo(Route.Auth.path) { inclusive = true }
                    }
                }
            }
            
            AuthScreen(authViewModel = authViewModel)
        }

        // Language Selection screen
        composable(Route.LanguageSelection.path) {
            LanguageSelectionScreen(
                onLanguageSelected = { language ->
                    // Navigate to practice selection screen
                    navController.navigate(Route.TtsMain.path) {
                        popUpTo(Route.LanguageSelection.path) { inclusive = true }
                    }
                },
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }

        // TTS Main screen
        composable(Route.TtsMain.path) {
            val ttsViewModel: TtsViewModel = hiltViewModel()
            val authState = authViewModel.authState.collectAsStateWithLifecycle()
            
            // Handle logout navigation
            LaunchedEffect(authState.value.isAuthenticated) {
                if (!authState.value.isAuthenticated) {
                    navController.navigate(Route.Auth.path) {
                        popUpTo(0) { inclusive = true } // Clear entire backstack
                    }
                }
            }
            
            TtsMainScreen(
                viewModel = ttsViewModel,
                authViewModel = authViewModel,
                inAppUpdateManager = inAppUpdateManager,
                onOpenSettings = { /* Settings removed - language selection handles configuration */ },
                onSwitchToSpeech = { navController.navigate(Route.SpeechMain.path) },
                onOpenListeningPractice = { navController.navigate(Route.ListeningSpeakingPractice.path) }
            )
        }
        

        
        // Speech Main screen
        composable(Route.SpeechMain.path) {
            val speechViewModel: SpeechViewModel = hiltViewModel()
            val authState = authViewModel.authState.collectAsStateWithLifecycle()

            // Register ViewModel for permission handling
            LaunchedEffect(speechViewModel) {
                onSpeechViewModelReady(speechViewModel)
            }

            // Handle logout navigation
            LaunchedEffect(authState.value.isAuthenticated) {
                if (!authState.value.isAuthenticated) {
                    navController.navigate(Route.Auth.path) {
                        popUpTo(0) { inclusive = true } // Clear entire backstack
                    }
                }
            }

            SpeechMainScreen(
                viewModel = speechViewModel,
                authViewModel = authViewModel,
                inAppUpdateManager = inAppUpdateManager,
                onOpenSettings = { /* Settings removed - language selection handles configuration */ },
                onSwitchToTts = { navController.navigate(Route.TtsMain.path) },
                onOpenConversationPractice = { navController.navigate(Route.ConversationPractice.path) },
                onRequestRecordAudioPermission = onRequestRecordAudioPermission
            )
        }
        
        // Listening/Speaking Practice screen
        composable(Route.ListeningSpeakingPractice.path) {
            ListeningSpeakingPracticeScreen(
                onClose = { navController.popBackStack() }
            )
        }

        // Conversation Practice screen
        composable(Route.ConversationPractice.path) {
            ConversationPracticeScreen(
                onClose = { navController.popBackStack() }
            )
        }


    }
}
