package com.tfkcolin.practice.navigation

/**
 * Sealed class representing all navigation routes in the app.
 * This provides type safety and prevents string duplication.
 */
sealed class Route(val path: String) {
    data object Auth : Route("auth")
    data object LanguageSelection : Route("language_selection")
    data object TtsMain : Route("tts_main")
    data object SpeechMain : Route("speech_main")
    data object ListeningSpeakingPractice : Route("listening_speaking_practice")
    data object ConversationPractice : Route("conversation_practice")
}
